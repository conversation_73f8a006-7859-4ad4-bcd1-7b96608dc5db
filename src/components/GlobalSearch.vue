<template>
  <div class="global-search" :class="{ 'mobile': isMobile }">
    <!-- 搜索输入框 -->
    <div class="search-input-container">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索店铺名称、地址或分类..."
        clearable
        @input="handleSearchInput"
        @focus="handleFocus"
        @blur="handleBlur"
        class="search-input"
        :prefix-icon="Search"
        size="large"
      >
        <template #suffix>
          <el-button
            v-if="searchKeyword && !isMobile"
            type="primary"
            size="small"
            @click="performSearch"
            :loading="searching"
          >
            搜索
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 搜索结果下拉列表 -->
    <div
      v-if="showResults && searchResults.length > 0"
      class="search-results"
      :class="{ 'mobile-results': isMobile }"
    >
      <div class="results-header">
        <span class="results-count">找到 {{ searchResults.length }} 个结果</span>
        <el-button
          type="text"
          size="small"
          @click="closeResults"
          class="close-btn"
        >
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
      
      <div class="results-list">
        <div
          v-for="(result, index) in searchResults"
          :key="result.id"
          class="result-item"
          @click="selectResult(result)"
        >
          <div class="result-content">
            <div class="result-header">
              <span class="result-name">{{ result.name }}</span>
              <span class="result-category" :style="{ color: getCategoryColor(result.category) }">
                {{ getCategoryIcon(result.category) }} {{ result.category }}
              </span>
            </div>
            <div class="result-address">
              <el-icon class="location-icon"><Location /></el-icon>
              {{ result.address }}
            </div>
            <div v-if="result.description" class="result-description">
              {{ result.description }}
            </div>
          </div>
          <div class="result-actions">
            <el-button
              type="primary"
              size="small"
              @click.stop="jumpToLocation(result)"
            >
              跳转
            </el-button>
          </div>
        </div>
      </div>

      <!-- 移动端底部操作 -->
      <div v-if="isMobile" class="mobile-actions">
        <el-button @click="closeResults" style="width: 100%">
          关闭搜索结果
        </el-button>
      </div>
    </div>

    <!-- 无结果提示 -->
    <div
      v-if="showResults && searchResults.length === 0 && searchKeyword.trim()"
      class="no-results"
      :class="{ 'mobile-results': isMobile }"
    >
      <el-empty
        description="未找到相关店铺"
        :image-size="60"
      >
        <el-button type="primary" @click="closeResults">
          关闭
        </el-button>
      </el-empty>
    </div>

    <!-- 移动端遮罩 -->
    <div
      v-if="isMobile && showResults"
      class="mobile-overlay"
      @click="closeResults"
    ></div>
  </div>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import { Search, Close, Location } from '@element-plus/icons-vue';
import shopService from '@/services/ShopService';
import categoryService from '@/services/CategoryService';

export default {
  name: 'GlobalSearch',
  components: {
    Search,
    Close,
    Location,
  },

  setup() {
    const store = useStore();
    
    // 响应式数据
    const searchKeyword = ref('');
    const searchResults = ref([]);
    const showResults = ref(false);
    const searching = ref(false);
    const inputFocused = ref(false);
    
    // 计算属性
    const isMobile = computed(() => store.getters['ui/isMobile']);
    
    // 防抖搜索
    let searchTimeout = null;
    
    // 获取分类颜色
    const getCategoryColor = (categoryName) => {
      const category = categoryService.getCategoryByName(categoryName);
      return category?.color || '#409eff';
    };
    
    // 获取分类图标
    const getCategoryIcon = (categoryName) => {
      const category = categoryService.getCategoryByName(categoryName);
      return category?.icon || '🍽️';
    };
    
    // 处理搜索输入
    const handleSearchInput = (value) => {
      // 清除之前的定时器
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
      
      // 如果输入为空，清空结果
      if (!value.trim()) {
        searchResults.value = [];
        showResults.value = false;
        return;
      }
      
      // 防抖搜索
      searchTimeout = setTimeout(() => {
        performSearch();
      }, 300);
    };
    
    // 执行搜索
    const performSearch = async () => {
      if (!searchKeyword.value.trim()) {
        return;
      }
      
      try {
        searching.value = true;
        
        // 使用 shopService 进行搜索
        const results = await shopService.searchShops(searchKeyword.value.trim());
        
        searchResults.value = results;
        showResults.value = true;
        
        // 移动端自动显示结果
        if (isMobile.value) {
          nextTick(() => {
            // 滚动到搜索结果
            const resultsElement = document.querySelector('.search-results');
            if (resultsElement) {
              resultsElement.scrollIntoView({ behavior: 'smooth' });
            }
          });
        }
        
      } catch (error) {
        ElMessage.error('搜索失败: ' + (error.message || '未知错误'));
        searchResults.value = [];
        showResults.value = false;
      } finally {
        searching.value = false;
      }
    };
    
    // 处理输入框聚焦
    const handleFocus = () => {
      inputFocused.value = true;
      // 如果有搜索结果，显示它们
      if (searchResults.value.length > 0) {
        showResults.value = true;
      }
    };
    
    // 处理输入框失焦
    const handleBlur = () => {
      inputFocused.value = false;
      // 延迟关闭结果，允许点击结果项
      setTimeout(() => {
        if (!inputFocused.value) {
          // showResults.value = false; // 不自动关闭，让用户手动关闭
        }
      }, 200);
    };
    
    // 选择搜索结果
    const selectResult = (result) => {
      jumpToLocation(result);
    };
    
    // 跳转到位置
    const jumpToLocation = (result) => {
      // 验证坐标
      const lng = parseFloat(result.lng || result.longitude);
      const lat = parseFloat(result.lat || result.latitude);
      
      if (isNaN(lng) || isNaN(lat)) {
        ElMessage.warning(`店铺"${result.name}"的位置信息不完整`);
        return;
      }
      
      // 更新地图中心点
      store.dispatch('ui/setMapState', {
        center: [lat, lng],
        zoom: 17
      });
      
      // 选择该店铺
      shopService.selectShop(result);
      store.dispatch('shops/selectShop', result.id);
      
      // 关闭搜索结果
      closeResults();
      
      // 移动端关闭侧边栏
      if (isMobile.value) {
        store.dispatch('ui/setSidebarCollapsed', true);
      }
      
      ElMessage.success(`已跳转到"${result.name}"`);
    };
    
    // 关闭搜索结果
    const closeResults = () => {
      showResults.value = false;
      searchKeyword.value = '';
      searchResults.value = [];
    };
    
    // 监听移动端状态变化
    watch(isMobile, (newVal) => {
      if (newVal && showResults.value) {
        // 移动端时确保结果可见
        nextTick(() => {
          const resultsElement = document.querySelector('.search-results');
          if (resultsElement) {
            resultsElement.scrollIntoView({ behavior: 'smooth' });
          }
        });
      }
    });
    
    return {
      searchKeyword,
      searchResults,
      showResults,
      searching,
      isMobile,
      getCategoryColor,
      getCategoryIcon,
      handleSearchInput,
      handleFocus,
      handleBlur,
      performSearch,
      selectResult,
      jumpToLocation,
      closeResults,
    };
  },
};
</script>

<style scoped>
.global-search {
  position: relative;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.search-input-container {
  position: relative;
  z-index: 1001;
}

.search-input {
  width: 100%;
}

.search-input :deep(.el-input__wrapper) {
  border-radius: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper):hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 20px rgba(64, 158, 255, 0.3);
}

/* 搜索结果样式 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-height: 400px;
  overflow: hidden;
  margin-top: 8px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
}

.results-count {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.close-btn {
  padding: 4px;
  min-height: auto;
}

.results-list {
  max-height: 320px;
  overflow-y: auto;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.result-item:hover {
  background-color: #f8f9fa;
}

.result-item:last-child {
  border-bottom: none;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.result-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.result-category {
  font-size: 12px;
  padding: 2px 6px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 4px;
  white-space: nowrap;
}

.result-address {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.location-icon {
  font-size: 12px;
  color: #999;
}

.result-description {
  color: #999;
  font-size: 12px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-actions {
  margin-left: 12px;
}

/* 无结果样式 */
.no-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 8px;
  padding: 20px;
}

/* 移动端样式 */
.mobile .search-input :deep(.el-input__wrapper) {
  border-radius: 20px;
}

.mobile-results {
  position: fixed !important;
  top: 60px !important;
  left: 10px !important;
  right: 10px !important;
  max-height: calc(100vh - 80px) !important;
  z-index: 2000 !important;
  margin-top: 0 !important;
}

.mobile-actions {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
}

.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1999;
}

/* 移动端结果项优化 */
.mobile .result-item {
  padding: 16px;
}

.mobile .result-header {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.mobile .result-name {
  font-size: 18px;
}

.mobile .result-address {
  font-size: 15px;
}

.mobile .result-actions {
  margin-left: 0;
  margin-top: 8px;
  width: 100%;
}

.mobile .result-actions .el-button {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .global-search {
    max-width: none;
  }
  
  .search-input {
    font-size: 16px; /* 防止iOS缩放 */
  }
}
</style>
